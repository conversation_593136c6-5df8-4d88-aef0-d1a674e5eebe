namespace MMO.Server.Systems
{
    /// <summary>
    /// Handles guilds, housing, and social features
    /// </summary>
    public interface ISocialSystem
    {
        Task InitializeAsync();
        Task UpdateAsync(float deltaTime);
    }
    
    public class SocialSystem : ISocialSystem
    {
        public async Task InitializeAsync()
        {
            // TODO: Implement social system
        }
        
        public async Task UpdateAsync(float deltaTime)
        {
            // TODO: Update guilds, housing, etc.
        }
    }
}
