namespace MMO.Server.Systems
{
    /// <summary>
    /// Handles economy, auction house, and trading
    /// </summary>
    public interface IEconomySystem
    {
        Task InitializeAsync();
        Task UpdateAsync(float deltaTime);
    }
    
    public class EconomySystem : IEconomySystem
    {
        public async Task InitializeAsync()
        {
            // TODO: Implement economy system
        }
        
        public async Task UpdateAsync(float deltaTime)
        {
            // TODO: Update auction house, process trades, etc.
        }
    }
}
